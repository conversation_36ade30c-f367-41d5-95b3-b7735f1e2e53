server:
  port: 8080

spring:
  application:
    name: user-auth-system

  main:
    allow-bean-definition-overriding: true

  # 數據庫配置
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: NDI1694T5q0WDy3p11bhtz4lo489W29D3Xd
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    open-in-view: false
    generate-ddl: true
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # 郵件配置
  mail:
    # Gmail 配置示例
    host: smtp.gmail.com
    port: 587
    username: ni<PERSON><PERSON>@gmail.com
    password: pddi avwc nxuw eqam

    # QQ郵箱配置示例（取消註釋使用）
    # host: smtp.qq.com
    # port: 587
    # username: <EMAIL>
    # password: your-qq-auth-code

    # 163郵箱配置示例（取消註釋使用）
    # host: smtp.163.com
    # port: 25
    # username: <EMAIL>
    # password: your-163-auth-code

    # Outlook/Hotmail 配置示例（取消註釋使用）
    # host: smtp-mail.outlook.com
    # port: 587
    # username: <EMAIL>
    # password: your-outlook-password

    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          timeout: 5000
          connectiontimeout: 5000
          writetimeout: 5000
          # 如果使用SSL，取消下面註釋
          # ssl:
          #   enable: true
          # socketFactory:
          #   class: javax.net.ssl.SSLSocketFactory
          #   port: 465
  
  # 文件上傳配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890
  access-token-expiration: 7200000  # 2小時 (2 * 60 * 60 * 1000)
  refresh-token-expiration: 604800000  # 7天 (7 * 24 * 60 * 60 * 1000)
  # 保留舊配置以兼容現有代碼
  expiration: 7200000 # 2小時

# 自定義配置
app:
  email:
    verification:
      expiration: 300000 # 5分鐘
      max-attempts-per-5min: 2
      max-attempts-per-day: 5
  
  file:
    upload-dir: ./uploads/
    max-size: 10MB

# 日誌配置
logging:
  level:
    com.example: DEBUG
    org.springframework.security: DEBUG
