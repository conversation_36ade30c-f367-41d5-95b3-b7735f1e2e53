package com.example.repository;

import com.example.entity.CartItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 購物車項目Repository
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Repository
public interface CartItemRepository extends JpaRepository<CartItem, Long> {
    
    /**
     * 根據購物車ID查找所有項目
     */
    List<CartItem> findByCartIdOrderByCreatedAtDesc(Long cartId);
    
    /**
     * 根據購物車ID和商品ID查找項目
     */
    Optional<CartItem> findByCartIdAndProductId(Long cartId, Long productId);
    
    /**
     * 根據購物車ID查找選中的項目
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.cartId = :cartId AND ci.selected = 1")
    List<CartItem> findSelectedItemsByCartId(@Param("cartId") Long cartId);
    
    /**
     * 根據購物車ID查找選中的項目（預加載商品信息）
     */
    @Query("SELECT ci FROM CartItem ci LEFT JOIN FETCH ci.product WHERE ci.cartId = :cartId AND ci.selected = 1")
    List<CartItem> findSelectedItemsByCartIdWithProduct(@Param("cartId") Long cartId);
    
    /**
     * 統計購物車中的商品數量
     */
    @Query("SELECT SUM(ci.quantity) FROM CartItem ci WHERE ci.cartId = :cartId")
    Integer sumQuantityByCartId(@Param("cartId") Long cartId);
    
    /**
     * 統計購物車中選中商品的數量
     */
    @Query("SELECT SUM(ci.quantity) FROM CartItem ci WHERE ci.cartId = :cartId AND ci.selected = 1")
    Integer sumSelectedQuantityByCartId(@Param("cartId") Long cartId);
    
    /**
     * 計算購物車選中商品的總金額
     */
    @Query("SELECT SUM(ci.price * ci.quantity) FROM CartItem ci WHERE ci.cartId = :cartId AND ci.selected = 1")
    java.math.BigDecimal sumSelectedAmountByCartId(@Param("cartId") Long cartId);
    
    /**
     * 根據購物車ID刪除所有項目
     */
    void deleteByCartId(Long cartId);
    
    /**
     * 根據購物車ID和商品ID刪除項目
     */
    void deleteByCartIdAndProductId(Long cartId, Long productId);
    
    /**
     * 批量刪除選中的項目
     */
    @Query("DELETE FROM CartItem ci WHERE ci.cartId = :cartId AND ci.selected = 1")
    void deleteSelectedItemsByCartId(@Param("cartId") Long cartId);
    
    /**
     * 檢查購物車中是否存在指定商品
     */
    boolean existsByCartIdAndProductId(Long cartId, Long productId);
    
    /**
     * 統計購物車中的商品種類數量
     */
    long countByCartId(Long cartId);
}
