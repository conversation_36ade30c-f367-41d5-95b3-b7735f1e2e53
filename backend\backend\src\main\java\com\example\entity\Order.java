package com.example.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 訂單實體類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Entity
@Table(name = "orders")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "user", "orderItems", "payment"})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 訂單號（唯一）
     */
    @Column(name = "order_number", nullable = false, unique = true, length = 32)
    private String orderNumber;
    
    /**
     * 用戶ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    /**
     * 訂單總金額
     */
    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;
    
    /**
     * 實付金額
     */
    @Column(name = "paid_amount", precision = 10, scale = 2)
    private BigDecimal paidAmount;
    
    /**
     * 訂單狀態：0-待付款，1-已付款，2-已發貨，3-已完成，-1-已取消
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;
    
    /**
     * 收貨人姓名
     */
    @Column(name = "receiver_name", length = 50)
    private String receiverName;
    
    /**
     * 收貨人電話
     */
    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;
    
    /**
     * 收貨地址
     */
    @Column(name = "receiver_address", length = 500)
    private String receiverAddress;
    
    /**
     * 訂單備註
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 支付時間
     */
    @Column(name = "paid_at")
    private LocalDateTime paidAt;
    
    /**
     * 發貨時間
     */
    @Column(name = "shipped_at")
    private LocalDateTime shippedAt;
    
    /**
     * 完成時間
     */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    /**
     * 取消時間
     */
    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;
    
    /**
     * 創建時間
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 關聯關係 - 用戶
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    // 關聯關係 - 訂單項目列表
    @OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<OrderItem> orderItems;
    
    // 關聯關係 - 支付記錄
    @OneToOne(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Payment payment;
    
    /**
     * 訂單狀態枚舉
     */
    public static class Status {
        public static final int CANCELLED = -1;    // 已取消
        public static final int PENDING_PAYMENT = 0;  // 待付款
        public static final int PAID = 1;          // 已付款
        public static final int SHIPPED = 2;       // 已發貨
        public static final int COMPLETED = 3;     // 已完成
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 構造函數 - 創建新訂單
     */
    public Order(String orderNumber, Long userId, BigDecimal totalAmount, String receiverName, String receiverPhone, String receiverAddress) {
        this.orderNumber = orderNumber;
        this.userId = userId;
        this.totalAmount = totalAmount;
        this.receiverName = receiverName;
        this.receiverPhone = receiverPhone;
        this.receiverAddress = receiverAddress;
        this.status = Status.PENDING_PAYMENT;
    }
}
