<template>
  <div class="product-category-tree">
    <div class="tree-header">
      <h3>商品分類</h3>
      <el-button 
        v-if="showRefresh" 
        link 
        :icon="Refresh" 
        @click="refreshTree"
        :loading="loading"
      >
        刷新
      </el-button>
    </div>
    
    <div class="tree-content">
      <!-- 全部分類選項 -->
      <div 
        class="category-item root-item"
        :class="{ active: selectedCategoryId === null }"
        @click="selectCategory(null)"
      >
        <el-icon><Grid /></el-icon>
        <span>全部分類</span>
        <span v-if="showCount" class="count">({{ totalProductCount }})</span>
      </div>
      
      <!-- 分類樹 -->
      <el-tree
        ref="treeRef"
        :data="categoryTree"
        :props="treeProps"
        :expand-on-click-node="false"
        :highlight-current="true"
        :current-node-key="selectedCategoryId"
        node-key="id"
        class="category-tree"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <el-icon v-if="data.iconUrl">
              <img :src="data.iconUrl" alt="" class="category-icon" />
            </el-icon>
            <el-icon v-else>
              <Folder v-if="!data.isLeaf" />
              <Document v-else />
            </el-icon>
            <span class="node-label">{{ node.label }}</span>
            <span v-if="showCount && productCounts[data.id]" class="count">
              ({{ productCounts[data.id] }})
            </span>
          </div>
        </template>
      </el-tree>
      
      <!-- 加載狀態 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <!-- 空狀態 -->
      <el-empty 
        v-if="!loading && categoryTree.length === 0" 
        description="暫無分類數據"
        :image-size="100"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { ElTree, ElMessage } from 'element-plus'
import { Refresh, Grid, Folder, Document } from '@element-plus/icons-vue'
import { useProductStore } from '../stores/product'
import { productCategoryAPI, type ProductCategory } from '../api/product'

// Props
interface Props {
  showRefresh?: boolean
  showCount?: boolean
  expandAll?: boolean
  defaultExpandedKeys?: number[]
}

const props = withDefaults(defineProps<Props>(), {
  showRefresh: true,
  showCount: true,
  expandAll: false,
  defaultExpandedKeys: () => []
})

// Emits
const emit = defineEmits<{
  categorySelect: [categoryId: number | null, category: ProductCategory | null]
}>()

// Store
const productStore = useProductStore()

// 響應式數據
const treeRef = ref<InstanceType<typeof ElTree>>()
const loading = ref(false)
const categoryTree = ref<ProductCategory[]>([])
const selectedCategoryId = ref<number | null>(null)
const productCounts = ref<Record<number, number>>({})
const totalProductCount = ref(0)

// 樹形組件配置
const treeProps = {
  children: 'children',
  label: 'name',
  disabled: (data: ProductCategory) => data.status === 0
}

// 計算屬性
const flatCategories = computed(() => {
  const result: ProductCategory[] = []
  const flatten = (categories: ProductCategory[]) => {
    categories.forEach(category => {
      result.push(category)
      if (category.children && category.children.length > 0) {
        flatten(category.children)
      }
    })
  }
  flatten(categoryTree.value)
  return result
})

// 加載分類樹
const loadCategoryTree = async () => {
  try {
    loading.value = true
    const response = await productCategoryAPI.getCategoryTree()
    
    if (response.success && response.data) {
      categoryTree.value = response.data
      
      // 如果需要展開所有節點
      if (props.expandAll) {
        await nextTick()
        const allKeys = flatCategories.value.map(cat => cat.id)
        treeRef.value?.setExpandedKeys(allKeys)
      } else if (props.defaultExpandedKeys.length > 0) {
        await nextTick()
        treeRef.value?.setExpandedKeys(props.defaultExpandedKeys)
      }
      
      // 加載商品數量統計
      if (props.showCount) {
        await loadProductCounts()
      }
    } else {
      throw new Error(response.message || '獲取分類樹失敗')
    }
  } catch (error: any) {
    ElMessage.error('獲取分類樹失敗: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加載商品數量統計
const loadProductCounts = async () => {
  try {
    const promises = flatCategories.value.map(async (category) => {
      try {
        const response = await productCategoryAPI.getProductCountByCategory(category.id)
        if (response.success && response.data !== undefined) {
          productCounts.value[category.id] = response.data
        }
      } catch (error) {
        // 忽略單個分類的統計錯誤
      }
    })
    
    await Promise.all(promises)
    
    // 計算總商品數量
    totalProductCount.value = Object.values(productCounts.value).reduce((sum, count) => sum + count, 0)
  } catch (error: any) {
    console.warn('獲取商品數量統計失敗:', error.message)
  }
}

// 處理節點點擊
const handleNodeClick = (data: ProductCategory) => {
  selectCategory(data.id, data)
}

// 選擇分類
const selectCategory = (categoryId: number | null, category: ProductCategory | null = null) => {
  selectedCategoryId.value = categoryId
  
  // 更新樹形組件的當前選中項
  if (categoryId) {
    treeRef.value?.setCurrentKey(categoryId)
  } else {
    treeRef.value?.setCurrentKey(null)
  }
  
  // 觸發事件
  emit('categorySelect', categoryId, category)
  
  // 更新store中的分類篩選
  productStore.setCategoryFilter(categoryId)
}

// 刷新樹
const refreshTree = async () => {
  await loadCategoryTree()
  ElMessage.success('分類樹已刷新')
}

// 獲取當前選中的分類
const getCurrentCategory = (): ProductCategory | null => {
  if (!selectedCategoryId.value) return null
  return flatCategories.value.find(cat => cat.id === selectedCategoryId.value) || null
}

// 展開指定節點
const expandNode = (categoryId: number) => {
  const node = treeRef.value?.getNode(categoryId)
  if (node) {
    node.expand()
  }
}

// 折疊指定節點
const collapseNode = (categoryId: number) => {
  const node = treeRef.value?.getNode(categoryId)
  if (node) {
    node.collapse()
  }
}

// 展開所有節點
const expandAll = () => {
  const allKeys = flatCategories.value.map(cat => cat.id)
  treeRef.value?.setExpandedKeys(allKeys)
}

// 折疊所有節點
const collapseAll = () => {
  treeRef.value?.setExpandedKeys([])
}

// 監聽store中的分類變化
watch(() => productStore.selectedCategoryId, (newCategoryId) => {
  if (newCategoryId !== selectedCategoryId.value) {
    selectedCategoryId.value = newCategoryId
    if (newCategoryId) {
      treeRef.value?.setCurrentKey(newCategoryId)
    }
  }
})

// 暴露方法給父組件
defineExpose({
  refreshTree,
  getCurrentCategory,
  expandNode,
  collapseNode,
  expandAll,
  collapseAll,
  selectCategory
})

// 組件掛載時加載數據
onMounted(() => {
  loadCategoryTree()
})
</script>

<style scoped>
.product-category-tree {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.tree-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.tree-content {
  padding: 8px;
  max-height: 500px;
  overflow-y: auto;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  margin-bottom: 4px;
}

.category-item:hover {
  background-color: #f5f7fa;
}

.category-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.category-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.category-item .count {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.category-tree {
  margin-top: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-node .el-icon {
  margin-right: 6px;
  font-size: 14px;
}

.category-icon {
  width: 14px;
  height: 14px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.count {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.loading-container {
  padding: 16px;
}

:deep(.el-tree-node__content) {
  height: 32px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e6f7ff;
  color: #1890ff;
}

:deep(.el-tree-node__expand-icon) {
  font-size: 12px;
}
</style>
