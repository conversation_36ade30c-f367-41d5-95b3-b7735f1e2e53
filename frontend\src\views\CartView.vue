<template>
  <div class="cart-view">
    <div class="container">
      <h1 class="page-title">購物車</h1>
      
      <!-- 購物車為空 -->
      <div v-if="cartItems.length === 0" class="empty-cart">
        <div class="empty-icon">🛒</div>
        <p>您的購物車還是空的</p>
        <router-link to="/products" class="btn btn-primary">去購物</router-link>
      </div>
      
      <!-- 購物車商品列表 -->
      <div v-else class="cart-content">
        <div class="cart-header">
          <label class="select-all">
            <input 
              type="checkbox" 
              v-model="selectAll" 
              @change="toggleSelectAll"
            />
            全選
          </label>
          <span class="item-count">共 {{ cartItems.length }} 件商品</span>
        </div>
        
        <div class="cart-items">
          <div 
            v-for="item in cartItems" 
            :key="item.id" 
            class="cart-item"
            :class="{ selected: item.selected }"
          >
            <label class="item-checkbox">
              <input 
                type="checkbox" 
                v-model="item.selected" 
                @change="toggleItemSelected(item)"
              />
            </label>
            
            <div class="item-image">
              <img :src="item.productImageUrl || '/default-product.jpg'" :alt="item.productName" />
            </div>
            
            <div class="item-info">
              <h3 class="item-name">{{ item.productName }}</h3>
              <p class="item-price">¥{{ item.price }}</p>
            </div>
            
            <div class="item-quantity">
              <button 
                class="quantity-btn" 
                @click="updateQuantity(item, item.quantity - 1)"
                :disabled="item.quantity <= 1"
              >
                -
              </button>
              <input 
                type="number" 
                v-model.number="item.quantity" 
                @change="updateQuantity(item, item.quantity)"
                min="1"
                class="quantity-input"
              />
              <button 
                class="quantity-btn" 
                @click="updateQuantity(item, item.quantity + 1)"
              >
                +
              </button>
            </div>
            
            <div class="item-subtotal">
              ¥{{ (item.price * item.quantity).toFixed(2) }}
            </div>
            
            <div class="item-actions">
              <button 
                class="btn btn-danger btn-sm" 
                @click="removeItem(item)"
              >
                刪除
              </button>
            </div>
          </div>
        </div>
        
        <!-- 購物車底部 -->
        <div class="cart-footer">
          <div class="cart-summary">
            <span class="selected-count">
              已選擇 {{ selectedItems.length }} 件商品
            </span>
            <span class="total-amount">
              合計：¥{{ totalAmount.toFixed(2) }}
            </span>
          </div>
          
          <div class="cart-actions">
            <button 
              class="btn btn-secondary" 
              @click="clearCart"
              :disabled="cartItems.length === 0"
            >
              清空購物車
            </button>
            <button 
              class="btn btn-primary btn-lg" 
              @click="goToCheckout"
              :disabled="selectedItems.length === 0"
            >
              去結算 ({{ selectedItems.length }})
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

interface CartItem {
  id: number
  productId: number
  productName: string
  productImageUrl: string
  price: number
  quantity: number
  selected: boolean
}

const router = useRouter()
const cartItems = ref<CartItem[]>([])
const loading = ref(false)

// 計算屬性
const selectedItems = computed(() => 
  cartItems.value.filter(item => item.selected)
)

const totalAmount = computed(() => 
  selectedItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
)

const selectAll = computed({
  get: () => cartItems.value.length > 0 && cartItems.value.every(item => item.selected),
  set: (value: boolean) => {
    cartItems.value.forEach(item => {
      item.selected = value
    })
  }
})

// 方法
const loadCart = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/cart', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data.cartItems) {
        cartItems.value = result.data.cartItems.map((item: any) => ({
          ...item,
          selected: item.selected === 1
        }))
      }
    }
  } catch (error) {
    console.error('加載購物車失敗:', error)
    ElMessage.error('加載購物車失敗')
  } finally {
    loading.value = false
  }
}

const toggleSelectAll = () => {
  const allSelected = selectAll.value
  cartItems.value.forEach(item => {
    if (item.selected !== allSelected) {
      toggleItemSelected(item)
    }
  })
}

const toggleItemSelected = async (item: CartItem) => {
  try {
    const response = await fetch(`/api/cart/toggle-selected/${item.id}?selected=${item.selected ? 1 : 0}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      // 如果請求失敗，恢復原狀態
      item.selected = !item.selected
      ElMessage.error('更新選中狀態失敗')
    }
  } catch (error) {
    item.selected = !item.selected
    ElMessage.error('更新選中狀態失敗')
  }
}

const updateQuantity = async (item: CartItem, newQuantity: number) => {
  if (newQuantity < 1) return
  
  const oldQuantity = item.quantity
  item.quantity = newQuantity
  
  try {
    const response = await fetch(`/api/cart/update/${item.id}?quantity=${newQuantity}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (!response.ok) {
      item.quantity = oldQuantity
      ElMessage.error('更新數量失敗')
    }
  } catch (error) {
    item.quantity = oldQuantity
    ElMessage.error('更新數量失敗')
  }
}

const removeItem = async (item: CartItem) => {
  try {
    await ElMessageBox.confirm('確定要刪除這個商品嗎？', '確認刪除', {
      type: 'warning'
    })
    
    const response = await fetch(`/api/cart/remove/${item.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      const index = cartItems.value.findIndex(i => i.id === item.id)
      if (index > -1) {
        cartItems.value.splice(index, 1)
      }
      ElMessage.success('商品已刪除')
    } else {
      ElMessage.error('刪除失敗')
    }
  } catch (error) {
    // 用戶取消刪除
  }
}

const clearCart = async () => {
  try {
    await ElMessageBox.confirm('確定要清空購物車嗎？', '確認清空', {
      type: 'warning'
    })
    
    const response = await fetch('/api/cart/clear', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.ok) {
      cartItems.value = []
      ElMessage.success('購物車已清空')
    } else {
      ElMessage.error('清空失敗')
    }
  } catch (error) {
    // 用戶取消清空
  }
}

const goToCheckout = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('請選擇要結算的商品')
    return
  }
  
  router.push('/checkout')
}

onMounted(() => {
  loadCart()
})
</script>

<style scoped>
.cart-view {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.cart-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.cart-item:hover {
  background-color: #f9f9f9;
}

.cart-item.selected {
  background-color: #f0f9ff;
}

.item-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.item-info {
  flex: 1;
  margin-left: 15px;
}

.item-name {
  font-size: 16px;
  margin-bottom: 8px;
}

.item-price {
  color: #e74c3c;
  font-weight: bold;
}

.item-quantity {
  display: flex;
  align-items: center;
  margin: 0 20px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
}

.quantity-input {
  width: 60px;
  height: 30px;
  text-align: center;
  border: 1px solid #ddd;
  border-left: none;
  border-right: none;
}

.item-subtotal {
  font-weight: bold;
  color: #e74c3c;
  margin-right: 20px;
}

.cart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f9f9f9;
}

.cart-summary {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-amount {
  font-size: 18px;
  font-weight: bold;
  color: #e74c3c;
}

.cart-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
