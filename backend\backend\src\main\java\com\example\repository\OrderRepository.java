package com.example.repository;

import com.example.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 訂單Repository
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    /**
     * 根據訂單號查找訂單
     */
    Optional<Order> findByOrderNumber(String orderNumber);
    
    /**
     * 根據用戶ID分頁查詢訂單
     */
    Page<Order> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    /**
     * 根據用戶ID和狀態查詢訂單
     */
    Page<Order> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Integer status, Pageable pageable);
    
    /**
     * 根據用戶ID查詢訂單（預加載訂單項目）
     */
    @Query("SELECT o FROM Order o LEFT JOIN FETCH o.orderItems WHERE o.userId = :userId ORDER BY o.createdAt DESC")
    List<Order> findByUserIdWithOrderItems(@Param("userId") Long userId);
    
    /**
     * 根據訂單ID查詢訂單（預加載訂單項目和支付信息）
     */
    @Query("SELECT o FROM Order o LEFT JOIN FETCH o.orderItems LEFT JOIN FETCH o.payment WHERE o.id = :orderId")
    Optional<Order> findByIdWithOrderItemsAndPayment(@Param("orderId") Long orderId);
    
    /**
     * 根據狀態查詢訂單
     */
    Page<Order> findByStatusOrderByCreatedAtDesc(Integer status, Pageable pageable);
    
    /**
     * 查詢指定時間範圍內的訂單
     */
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN :startTime AND :endTime ORDER BY o.createdAt DESC")
    List<Order> findByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 統計用戶的訂單數量
     */
    long countByUserId(Long userId);
    
    /**
     * 統計用戶指定狀態的訂單數量
     */
    long countByUserIdAndStatus(Long userId, Integer status);
    
    /**
     * 查詢超時未支付的訂單
     */
    @Query("SELECT o FROM Order o WHERE o.status = 0 AND o.createdAt < :timeoutTime")
    List<Order> findTimeoutUnpaidOrders(@Param("timeoutTime") LocalDateTime timeoutTime);
    
    /**
     * 統計總訂單數量
     */
    @Query("SELECT COUNT(o) FROM Order o")
    long countTotalOrders();
    
    /**
     * 統計各狀態訂單數量
     */
    @Query("SELECT o.status, COUNT(o) FROM Order o GROUP BY o.status")
    List<Object[]> countOrdersByStatus();
    
    /**
     * 計算用戶總消費金額
     */
    @Query("SELECT SUM(o.paidAmount) FROM Order o WHERE o.userId = :userId AND o.status IN (1, 2, 3)")
    java.math.BigDecimal sumPaidAmountByUserId(@Param("userId") Long userId);
    
    /**
     * 查詢用戶最近的訂單
     */
    @Query("SELECT o FROM Order o WHERE o.userId = :userId ORDER BY o.createdAt DESC")
    List<Order> findRecentOrdersByUserId(@Param("userId") Long userId, Pageable pageable);
}
