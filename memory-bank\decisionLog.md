# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-10 15:48:02 - Log of updates made.

## [2025-07-15 09:51:41] 修復管理員後台查看詳情的 Hibernate 懶加載問題

### Decision
在 IdentityVerificationRepository 中添加 findByIdWithUser 方法，使用 JOIN FETCH 預加載 User 實體，解決管理員後台查看身份認證詳情時的懶加載錯誤。

### Rationale
- 原問題：管理員點擊"查看詳情"時出現 "Could not initialize proxy [com.example.entity.User#3] - no Session" 錯誤
- 根本原因：IdentityVerification 實體中的 User 屬性是懶加載的，當 Hibernate Session 關閉後無法訪問
- 解決方案：使用 JOIN FETCH 在查詢時預加載關聯實體，避免懶加載問題
- 優點：解決懶加載問題，提高查詢效率，避免 N+1 查詢問題

### Implementation Details
- 在 IdentityVerificationRepository 中添加了 findByIdWithUser 方法：
  ```java
  @Query("SELECT iv FROM IdentityVerification iv JOIN FETCH iv.user WHERE iv.id = :id")
  Optional<IdentityVerification> findByIdWithUser(@Param("id") Long id);
  ```
- 修改 IdentityVerificationService 中的 getVerificationById 方法使用新的查詢方法
- 添加了必要的 @Param 導入

## [2025-07-10 20:58:31] 修復 Hibernate 懶加載問題

### Decision
使用 JOIN FETCH 查詢來預加載 User 實體，避免在 Controller 中訪問懶加載屬性時出現 "could not initialize proxy" 錯誤。

### Rationale
- 原問題：AdminIdentityController 中的 convertToMapWithUserInfo 方法嘗試訪問 IdentityVerification.user 的屬性，但 User 是懶加載的，當 Hibernate Session 關閉後無法訪問
- 解決方案：在 Repository 層使用 @Query 註解和 JOIN FETCH 來預加載關聯實體
- 優點：解決懶加載問題，提高查詢效率，避免 N+1 查詢問題

### Implementation Details
- 在 IdentityVerificationRepository 中添加了兩個新方法：
  - findByStatusWithUserOrderBySubmittedAtAsc：預加載 User 的待審核查詢
  - findAllWithUserOrderBySubmittedAtDesc：預加載 User 的全部查詢
- 修改 IdentityVerificationService 中的 getPendingVerifications 和 getAllVerifications 方法使用新的查詢方法
- 移除了 AdminIdentityController 中的調試日誌，保持代碼整潔