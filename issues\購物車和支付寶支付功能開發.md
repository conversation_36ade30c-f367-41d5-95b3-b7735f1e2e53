# 購物車和支付寶支付功能開發

## 項目背景
基於現有的Vue3 + TypeScript + SpringBoot + Redis項目，添加完整的電商購物車和支付寶支付功能。

## 技術架構
- 後端：SpringBoot + JPA + MySQL + Redis
- 前端：Vue3 + TypeScript + Pinia
- 支付：支付寶沙箱環境
- 緩存：Redis用於購物車臨時存儲

## 開發計劃

### 階段1：後端數據庫設計和實體創建
1. Cart實體 - 購物車主表
2. CartItem實體 - 購物車項目
3. Order實體 - 訂單主表  
4. OrderItem實體 - 訂單項目
5. Payment實體 - 支付記錄

### 階段2：支付寶配置和依賴
1. 添加支付寶SDK依賴
2. 創建支付寶配置類
3. 配置application.yml

### 階段3-8：Repository、Service、Controller、前端開發

## 支付寶配置信息
- 應用ID: 9021000129631387
- 沙箱網關: https://openapi-sandbox.dl.alipaydev.com/gateway.do
- 商戶PID: 2088721074001026
- 回調地址: 使用ngrok提供的公網地址

## 預期功能
1. 商品選擇和加入購物車
2. 購物車管理（增刪改查）
3. 訂單創建和管理
4. 支付寶支付集成
5. 支付回調處理
6. 訂單狀態跟蹤
